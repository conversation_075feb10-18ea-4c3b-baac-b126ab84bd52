import { ElMessage } from 'element-plus'
import { useUploadStore, UploadStatus, type UploadTask } from '@/store/modules/upload'
import { createFileFingerprint, createFileChunks, calculateProgress } from '@/utils/uploadHelper'
import * as MultipartUploadApi from '@/api/infra/multipartUpload'

/**
 * 断点续传上传服务
 */
export class ResumableUploadService {
  private uploadStore = useUploadStore()
  private maxConcurrentUploads = 3 // 最大并发上传数
  private chunkSize = 5 * 1024 * 1024 // 5MB分片大小

  /**
   * 开始上传文件
   * @param file 要上传的文件
   * @param datasetId 数据集ID（可选）
   * @param onProgress 进度回调
   * @param onSuccess 成功回调（fileUrl, fileInfo）
   * @param onError 错误回调
   */
  async startUpload(
    file: File,
    datasetId?: number,
    onProgress?: (progress: number) => void,
    onSuccess?: (fileUrl: string, fileInfo?: any) => void,
    onError?: (error: string) => void
  ): Promise<void> {
    const fingerprint = createFileFingerprint(file)

    // 检查是否已有任务在进行
    const existingTask = this.uploadStore.getTask(fingerprint)
    if (existingTask && existingTask.status === UploadStatus.UPLOADING) {
      ElMessage.warning('该文件正在上传中')
      return
    }

    // 创建上传任务
    const chunks = createFileChunks(file, this.chunkSize)
    const task: UploadTask = {
      id: fingerprint,
      file,
      fingerprint,
      status: UploadStatus.WAITING,
      progress: 0,
      chunks,
      completedChunks: new Set(),
      startTime: Date.now()
    }

    this.uploadStore.addTask(task)

    try {
      await this.executeUpload(task, onProgress, onSuccess, onError)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败'
      this.uploadStore.setTaskError(fingerprint, errorMessage)
      onError?.(errorMessage)
    }
  }

  /**
   * 执行上传逻辑
   */
  private async executeUpload(
    task: UploadTask,
    onProgress?: (progress: number) => void,
    onSuccess?: (fileUrl: string) => void,
    onError?: (error: string) => void
  ): Promise<void> {
    const { fingerprint, file } = task

    // 1. 获取或创建 uploadId
    let uploadId = localStorage.getItem(fingerprint)
    if (!uploadId) {
      try {
        const response = await MultipartUploadApi.initMultipartUpload(file.name)
        uploadId = response.uploadId
        localStorage.setItem(fingerprint, uploadId)
        this.uploadStore.updateTaskUploadId(fingerprint, uploadId)
      } catch (error) {
        console.error('初始化上传失败:', error)
        throw new Error('初始化上传失败')
      }
    } else {
      this.uploadStore.updateTaskUploadId(fingerprint, uploadId)
    }

    // 2. 获取已上传的分片列表
    let completedParts: number[] = []
    try {
      const response = await MultipartUploadApi.getPartList(uploadId)
      completedParts = response.map((part) => part.partNumber)

      // 更新任务中的已完成分片
      completedParts.forEach((partNumber) => {
        task.completedChunks.add(partNumber)
      })

      // 更新进度
      const progress = calculateProgress(completedParts.length, task.chunks.length)
      this.uploadStore.updateTaskProgress(fingerprint, progress)
      onProgress?.(progress)
    } catch (error) {
      console.error('获取已上传分片列表失败:', error)

      // part-list 接口失败表示系统性问题，必须停止上传流程
      throw new Error('获取分片列表失败，上传已停止。请重新开始上传')
    }

    // 3. 筛选需要上传的分片
    const chunksToUpload = task.chunks.filter((chunk) => !completedParts.includes(chunk.index))

    if (chunksToUpload.length === 0) {
      // 所有分片都已上传，直接合并
      await this.completeUpload(task, onSuccess, onError)
      return
    }

    // 4. 开始上传剩余分片
    this.uploadStore.updateTaskStatus(fingerprint, UploadStatus.UPLOADING)

    await this.uploadChunksWithConcurrency(task, chunksToUpload, onProgress)
    await this.completeUpload(task, onSuccess, onError)
  }

  /**
   * 并发上传分片
   */
  private async uploadChunksWithConcurrency(
    task: UploadTask,
    chunksToUpload: Array<{ chunk: Blob; index: number }>,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    const { fingerprint, uploadId } = task

    if (!uploadId) {
      throw new Error('上传ID不存在')
    }

    // 使用Promise.all控制并发数量
    const uploadPromises: Promise<void>[] = []

    for (let i = 0; i < chunksToUpload.length; i += this.maxConcurrentUploads) {
      const batch = chunksToUpload.slice(i, i + this.maxConcurrentUploads)

      const batchPromises = batch.map(async ({ chunk, index }) => {
        try {
          await MultipartUploadApi.uploadPart(uploadId, index, chunk)
          this.uploadStore.markChunkCompleted(fingerprint, index)

          // 更新进度
          const progress = calculateProgress(task.completedChunks.size, task.chunks.length)
          onProgress?.(progress)
        } catch (error) {
          console.error(`分片 ${index} 上传失败:`, error)
          throw new Error(`分片 ${index} 上传失败`)
        }
      })

      uploadPromises.push(...batchPromises)

      // 等待当前批次完成再继续下一批次
      await Promise.all(batchPromises)
    }
  }

  /**
   * 完成上传（合并分片）
   */
  private async completeUpload(
    task: UploadTask,
    onSuccess?: (fileUrl: string) => void,
    onError?: (error: string) => void
  ): Promise<void> {
    const { fingerprint, uploadId } = task

    if (!uploadId) {
      throw new Error('上传ID不存在')
    }

    try {
      const fileInfo = await MultipartUploadApi.completeMultipartUpload(uploadId)
      const fileUrl = fileInfo.url

      // 清理本地存储
      localStorage.removeItem(fingerprint)

      // 更新任务状态
      this.uploadStore.updateTaskStatus(fingerprint, UploadStatus.SUCCESS)

      onSuccess?.(fileUrl, fileInfo)
      ElMessage.success('文件上传成功')
    } catch (error) {
      console.error('合并文件失败:', error)
      const errorMessage = '合并文件失败'
      this.uploadStore.setTaskError(fingerprint, errorMessage)
      onError?.(errorMessage)
      throw new Error(errorMessage)
    }
  }

  /**
   * 暂停上传
   */
  pauseUpload(fingerprint: string): void {
    this.uploadStore.updateTaskStatus(fingerprint, UploadStatus.PAUSED)
  }

  /**
   * 恢复上传
   */
  async resumeUpload(fingerprint: string): Promise<void> {
    const task = this.uploadStore.getTask(fingerprint)
    if (!task) {
      throw new Error('任务不存在')
    }

    await this.executeUpload(task)
  }

  /**
   * 取消上传
   */
  async cancelUpload(fingerprint: string): Promise<void> {
    const task = this.uploadStore.getTask(fingerprint)
    if (!task || !task.uploadId) {
      this.uploadStore.removeTask(fingerprint)
      return
    }

    try {
      await MultipartUploadApi.abortMultipartUpload(task.file.name, task.uploadId)
      localStorage.removeItem(fingerprint)
      this.uploadStore.removeTask(fingerprint)
      ElMessage.success('上传已取消')
    } catch (error) {
      console.error('取消上传失败:', error)
      this.uploadStore.updateTaskStatus(fingerprint, UploadStatus.CANCELLED)
    }
  }
}

// 导出单例实例
export const resumableUploadService = new ResumableUploadService()
