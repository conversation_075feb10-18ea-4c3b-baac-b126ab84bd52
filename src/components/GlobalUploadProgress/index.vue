<template>
  <div v-if="hasUploadingTasks" class="global-upload-progress">
    <div class="upload-header">
      <div class="upload-title">
        <Icon icon="ep:upload" />
        <span>文件上传 ({{ uploadingTasks.length }})</span>
      </div>
      <div class="upload-actions">
        <el-button text @click="toggleExpanded">
          {{ isExpanded ? '收起' : '展开' }}
        </el-button>
        <el-button text @click="clearCompleted">清理已完成</el-button>
      </div>
    </div>
    
    <div v-if="isExpanded" class="upload-list">
      <div
        v-for="task in visibleTasks"
        :key="task.fingerprint"
        class="upload-item"
        :class="{ 'upload-error': task.status === 'error' }"
      >
        <div class="file-info">
          <div class="file-name">{{ task.file.name }}</div>
          <div class="file-size">{{ formatFileSize(task.file.size) }}</div>
        </div>
        
        <div class="upload-progress">
          <el-progress
            :percentage="task.progress"
            :status="getProgressStatus(task.status)"
            :stroke-width="4"
          />
          <div class="progress-text">
            <span>{{ task.progress }}%</span>
            <span v-if="task.status === 'error'" class="error-text">{{ task.error }}</span>
            <span v-else-if="task.status === 'success'" class="success-text">上传完成</span>
            <span v-else-if="task.status === 'uploading'" class="uploading-text">上传中...</span>
            <span v-else-if="task.status === 'paused'" class="paused-text">已暂停</span>
          </div>
        </div>
        
        <div class="upload-actions">
          <el-button
            v-if="task.status === 'uploading'"
            text
            size="small"
            @click="pauseUpload(task.fingerprint)"
          >
            暂停
          </el-button>
          <el-button
            v-else-if="task.status === 'paused'"
            text
            size="small"
            @click="resumeUpload(task.fingerprint)"
          >
            继续
          </el-button>
          <el-button
            v-if="task.status === 'error'"
            text
            size="small"
            @click="retryUpload(task.fingerprint)"
          >
            重试
          </el-button>
          <el-button
            text
            size="small"
            @click="cancelUpload(task.fingerprint)"
          >
            {{ task.status === 'success' ? '移除' : '取消' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUploadStore, UploadStatus } from '@/store/modules/upload'
import { resumableUploadService } from '@/services/resumableUploadService'
import { formatFileSize } from '@/utils/uploadHelper'

defineOptions({ name: 'GlobalUploadProgress' })

const uploadStore = useUploadStore()
const isExpanded = ref(true)

// 计算属性
const uploadingTasks = computed(() => uploadStore.getUploadingTasks)
const allTasks = computed(() => uploadStore.getAllTasks)
const hasUploadingTasks = computed(() => allTasks.value.length > 0)

// 显示的任务列表（最多显示5个）
const visibleTasks = computed(() => {
  return allTasks.value.slice(0, 5)
})

// 获取进度条状态
const getProgressStatus = (status: UploadStatus) => {
  switch (status) {
    case UploadStatus.SUCCESS:
      return 'success'
    case UploadStatus.ERROR:
      return 'exception'
    case UploadStatus.UPLOADING:
      return undefined
    default:
      return undefined
  }
}

// 切换展开/收起
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 清理已完成的任务
const clearCompleted = () => {
  uploadStore.clearCompletedTasks()
}

// 暂停上传
const pauseUpload = (fingerprint: string) => {
  resumableUploadService.pauseUpload(fingerprint)
}

// 恢复上传
const resumeUpload = async (fingerprint: string) => {
  try {
    await resumableUploadService.resumeUpload(fingerprint)
  } catch (error) {
    console.error('恢复上传失败:', error)
  }
}

// 重试上传
const retryUpload = async (fingerprint: string) => {
  const task = uploadStore.getTask(fingerprint)
  if (task) {
    try {
      await resumableUploadService.startUpload(task.file)
    } catch (error) {
      console.error('重试上传失败:', error)
    }
  }
}

// 取消上传
const cancelUpload = async (fingerprint: string) => {
  try {
    await resumableUploadService.cancelUpload(fingerprint)
  } catch (error) {
    console.error('取消上传失败:', error)
  }
}
</script>

<style scoped>
.global-upload-progress {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 400px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  border: 1px solid #e4e7ed;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;
  border-radius: 8px 8px 0 0;
}

.upload-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #303133;
}

.upload-actions {
  display: flex;
  gap: 8px;
}

.upload-list {
  max-height: 300px;
  overflow-y: auto;
}

.upload-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.upload-item:hover {
  background-color: #f9f9f9;
}

.upload-item:last-child {
  border-bottom: none;
}

.upload-item.upload-error {
  background-color: #fef0f0;
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.file-name {
  font-weight: 500;
  color: #303133;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 12px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.upload-progress {
  margin-bottom: 8px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
}

.error-text {
  color: #f56c6c;
}

.success-text {
  color: #67c23a;
}

.uploading-text {
  color: #409eff;
}

.paused-text {
  color: #e6a23c;
}

.upload-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
