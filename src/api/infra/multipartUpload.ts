import request from '@/config/axios'

/**
 * 分片上传相关API
 */

// 分片信息接口
export interface PartInfo {
  partNumber: number
  eTag: string
  size?: number
  lastModified?: string
  checksumCRC32?: string
  checksumCRC32C?: string
  checksumCRC64NVME?: string
  checksumSHA1?: string
  checksumSHA256?: string
}

// 初始化分片上传响应
export interface InitMultipartUploadResponse {
  objectKey: string
  uploadId: string
  partNumber: number
  multipartFile: any
  partCount: number
  partSize: any
  fileSize: any
  fileName: any
  mime: any
  s3PartDTOS: any[]
}

/**
 * 初始化分片上传
 * @param objectKey 对象键(文件在存储桶中的路径)
 */
export const initMultipartUpload = (objectKey: string): Promise<InitMultipartUploadResponse> => {
  return request.get({
    url: '/infra/file/init-multipart-upload',
    params: { objectKey }
  })
}

/**
 * 上传分片
 * @param uploadId 上传ID
 * @param partNumber 分片编号
 * @param multipartFile 分片文件
 */
export const uploadPart = (uploadId: string, partNumber: number, multipartFile: Blob) => {
  const formData = new FormData()
  formData.append('uploadId', uploadId)
  formData.append('partNumber', partNumber.toString())
  formData.append('multipartFile', multipartFile)

  return request.post({
    url: '/infra/file/upload-part',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取已上传的分片列表
 * @param uploadId 上传ID
 */
export const getPartList = (uploadId: string): Promise<PartInfo[]> => {
  return request.get({
    url: '/infra/file/part-list',
    params: { uploadId }
  })
}

// 完成分片上传响应
export interface CompleteMultipartUploadResponse {
  id: number
  configId: number
  path: string
  name: string
  url: string
  type: string
  size: number
  createTime: number
}

/**
 * 完成分片上传(合并分片)
 * @param uploadId 上传ID
 */
export const completeMultipartUpload = (
  uploadId: string
): Promise<CompleteMultipartUploadResponse> => {
  return request.post({
    url: '/infra/file/complete-upload-part',
    params: { uploadId }
  })
}

/**
 * 取消分片上传
 * @param objectKey 对象键
 * @param uploadId 上传ID
 */
export const abortMultipartUpload = (objectKey: string, uploadId: string) => {
  return request.get({
    url: '/infra/file/abort-upload-part',
    params: { objectKey, uploadId }
  })
}
