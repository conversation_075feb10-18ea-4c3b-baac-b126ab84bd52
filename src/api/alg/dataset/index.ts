import request from '@/config/axios'

// 创建数据集
export const createDataset = (data) => {
  return request.post({
    url: '/alg/dataset/create',
    data
  })
}

// 更新数据集
export const updateDataset = (data) => {
  return request.put({
    url: '/admin-api/alg/dataset/update',
    data
  })
}

// 删除数据集
export const deleteDataset = (params) => {
  return request.delete({
    url: '/alg/dataset/delete',
    params
  })
}

// 批量删除数据集
export const deleteDatasetList = (params) => {
  return request.delete({
    url: '/alg/dataset/delete-list',
    params
  })
}

// 获得数据集
export const getDataset = (params) => {
  return request.get({
    url: '/alg/dataset/get',
    params
  })
}

// 获得数据集分页
export const getDatasetPage = (params) => {
  return request.get({
    url: '/alg/dataset/page',
    params
  })
}

// 导出数据集 Excel
export const exportDatasetExcel = (params) => {
  return request.download({
    url: '/admin-api/alg/dataset/export-excel',
    params
  })
}

// 数据集文件相关接口

// 数据集文件接口
export interface DatasetFile {
  id: string
  datasetId: number
  datasetIds: number[] | null
  fileName: string
  type: number
  fileId: number
  aiProcessed: number
  createTime: number
  updateTime: number
  updater: string
  deleted: string
  createDept: number | null
  creator: string
  fileUrl: string
  fileSize: number
}

// 文件信息接口
export interface FileVo {
  fileName: string
  fileType: number
  fileId: number
}

// 创建数据集文件请求接口
export interface DatasetFilesSaveReqVO {
  datasetId: string
  fileVos: FileVo[]
}

// 数据集文件分页查询参数
export interface DatasetFilesPageParams {
  pageNo: number
  pageSize: number
  datasetId?: number
  fileName?: string
  type?: number
  fileId?: number
  aiProcessed?: number
}

// 创建数据集文件
export const createDatasetFiles = (data: DatasetFilesSaveReqVO) => {
  return request.post({
    url: '/alg/dataset-files/createDatasetFiles',
    data
  })
}

// 获得数据集文件分页
export const getDatasetFilesPage = (params: DatasetFilesPageParams) => {
  return request.get({
    url: '/alg/dataset-files/page',
    params
  })
}

// 批量删除数据集文件
export const deleteDatasetFilesList = (ids: string[]) => {
  return request.delete({
    url: '/alg/dataset-files/delete-list',
    params: { ids: ids.join(',') }
  })
}
