/**
 * 文件上传工具函数
 */

/**
 * 创建文件唯一指纹 (文件的"身份证")
 * @param {File} file
 * @returns {string}
 */
export function createFileFingerprint(file: File): string {
  return `upload-fingerprint-${file.name}-${file.size}-${file.lastModified}`
}

/**
 * 对文件进行逻辑切片 (创建"切片计划")
 * @param {File} file
 * @param {number} chunkSize 分片大小，默认5MB
 * @returns {Array<{chunk: Blob, index: number}>}
 */
export function createFileChunks(file: File, chunkSize: number = 5 * 1024 * 1024) {
  const chunks: Array<{ chunk: Blob; index: number }> = []
  const totalChunks = Math.ceil(file.size / chunkSize)
  
  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize
    const end = Math.min(start + chunkSize, file.size)
    chunks.push({
      index: i + 1, // 分片编号从1开始
      chunk: file.slice(start, end)
    })
  }
  
  return chunks
}

/**
 * 格式化文件大小
 * @param {number} bytes
 * @returns {string}
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 计算上传进度
 * @param {number} completedParts 已完成的分片数
 * @param {number} totalParts 总分片数
 * @returns {number} 进度百分比 (0-100)
 */
export function calculateProgress(completedParts: number, totalParts: number): number {
  if (totalParts === 0) return 0
  return Math.round((completedParts / totalParts) * 100)
}

/**
 * 检查文件类型是否支持
 * @param {File} file
 * @param {string[]} allowedTypes 允许的文件类型
 * @returns {boolean}
 */
export function isFileTypeAllowed(file: File, allowedTypes: string[]): boolean {
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  if (!fileExtension) return false
  
  return allowedTypes.some(type => type.toLowerCase() === fileExtension)
}

/**
 * 检查文件大小是否超限
 * @param {File} file
 * @param {number} maxSizeInMB 最大文件大小(MB)
 * @returns {boolean}
 */
export function isFileSizeAllowed(file: File, maxSizeInMB: number): boolean {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024
  return file.size <= maxSizeInBytes
}
