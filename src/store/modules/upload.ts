import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { store } from '@/store'

// 上传任务状态
export enum UploadStatus {
  WAITING = 'waiting',     // 等待上传
  UPLOADING = 'uploading', // 上传中
  PAUSED = 'paused',       // 已暂停
  SUCCESS = 'success',     // 上传成功
  ERROR = 'error',         // 上传失败
  CANCELLED = 'cancelled'  // 已取消
}

// 上传任务数据结构
export interface UploadTask {
  id: string                    // 任务ID（文件指纹）
  file: File                   // 文件对象
  fingerprint: string          // 文件指纹
  status: UploadStatus         // 任务状态
  progress: number             // 上传进度 0-100
  uploadId?: string            // 上传凭证
  chunks: Array<{              // 分片信息
    chunk: Blob
    index: number
  }>
  completedChunks: Set<number> // 已完成的分片编号
  error?: string               // 错误信息
  startTime?: number           // 开始时间
  endTime?: number             // 结束时间
  speed?: number               // 上传速度 (bytes/s)
  remainingTime?: number       // 剩余时间 (秒)
}

export const useUploadStore = defineStore('upload', () => {
  // 上传任务列表
  const uploadTasks = ref<Map<string, UploadTask>>(new Map())
  
  // 当前活跃的上传任务数量
  const activeTasksCount = computed(() => {
    let count = 0
    uploadTasks.value.forEach(task => {
      if (task.status === UploadStatus.UPLOADING) {
        count++
      }
    })
    return count
  })
  
  // 获取所有任务
  const getAllTasks = computed(() => {
    return Array.from(uploadTasks.value.values())
  })
  
  // 获取正在上传的任务
  const getUploadingTasks = computed(() => {
    return Array.from(uploadTasks.value.values()).filter(
      task => task.status === UploadStatus.UPLOADING
    )
  })
  
  // 添加上传任务
  const addTask = (task: UploadTask) => {
    uploadTasks.value.set(task.fingerprint, task)
  }
  
  // 获取上传任务
  const getTask = (fingerprint: string): UploadTask | undefined => {
    return uploadTasks.value.get(fingerprint)
  }
  
  // 更新任务状态
  const updateTaskStatus = (fingerprint: string, status: UploadStatus) => {
    const task = uploadTasks.value.get(fingerprint)
    if (task) {
      task.status = status
      if (status === UploadStatus.SUCCESS) {
        task.endTime = Date.now()
        task.progress = 100
      } else if (status === UploadStatus.ERROR || status === UploadStatus.CANCELLED) {
        task.endTime = Date.now()
      }
    }
  }
  
  // 更新任务进度
  const updateTaskProgress = (fingerprint: string, progress: number) => {
    const task = uploadTasks.value.get(fingerprint)
    if (task) {
      task.progress = progress
    }
  }
  
  // 更新任务上传ID
  const updateTaskUploadId = (fingerprint: string, uploadId: string) => {
    const task = uploadTasks.value.get(fingerprint)
    if (task) {
      task.uploadId = uploadId
    }
  }
  
  // 标记分片完成
  const markChunkCompleted = (fingerprint: string, chunkIndex: number) => {
    const task = uploadTasks.value.get(fingerprint)
    if (task) {
      task.completedChunks.add(chunkIndex)
      // 更新进度
      const progress = Math.round((task.completedChunks.size / task.chunks.length) * 100)
      task.progress = progress
    }
  }
  
  // 设置任务错误
  const setTaskError = (fingerprint: string, error: string) => {
    const task = uploadTasks.value.get(fingerprint)
    if (task) {
      task.error = error
      task.status = UploadStatus.ERROR
      task.endTime = Date.now()
    }
  }
  
  // 移除任务
  const removeTask = (fingerprint: string) => {
    uploadTasks.value.delete(fingerprint)
  }
  
  // 清空所有任务
  const clearAllTasks = () => {
    uploadTasks.value.clear()
  }
  
  // 清空已完成的任务
  const clearCompletedTasks = () => {
    const tasksToRemove: string[] = []
    uploadTasks.value.forEach((task, fingerprint) => {
      if (task.status === UploadStatus.SUCCESS || 
          task.status === UploadStatus.ERROR || 
          task.status === UploadStatus.CANCELLED) {
        tasksToRemove.push(fingerprint)
      }
    })
    tasksToRemove.forEach(fingerprint => {
      uploadTasks.value.delete(fingerprint)
    })
  }
  
  return {
    uploadTasks,
    activeTasksCount,
    getAllTasks,
    getUploadingTasks,
    addTask,
    getTask,
    updateTaskStatus,
    updateTaskProgress,
    updateTaskUploadId,
    markChunkCompleted,
    setTaskError,
    removeTask,
    clearAllTasks,
    clearCompletedTasks
  }
})

export const useUploadStoreWithOut = () => {
  return useUploadStore(store)
}
