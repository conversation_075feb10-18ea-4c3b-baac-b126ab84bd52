import { DATASET_TYPES } from './constants'

export type DatasetType = (typeof DATASET_TYPES)[keyof typeof DATASET_TYPES]

export interface DatasetFile {
  id: string
  datasetId: number
  datasetIds: number[] | null
  fileName: string
  type: number
  fileId: number
  aiProcessed: number
  createTime: number
  updateTime: number
  updater: string
  deleted: string
  createDept: number | null
  creator: string
  fileUrl: string
  fileSize: number
}

export interface Dataset {
  id: number
  name: string
  type: DatasetType
  dataSourceSize: number
  dataSourceNum: number
  creatorName: string
  coverImageFileUrl: string
  status: number
  markFlag: number
  createTime: string
  creator: string
  updater: string
  deleted: string
  datasetFiles: DatasetFile[]
  coverImageFileId: number
}

export interface QueryParams {
  pageNo: number
  pageSize: number
  name?: string
  type?: DatasetType
}

export interface CreateDatasetForm {
  name: string
  type: DatasetType | undefined
  coverUrl?: string
}
