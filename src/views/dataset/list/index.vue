<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container mb-3">
      <Filter :query-params="queryParams" @query="handleQuery" @reset="resetQuery" />
    </div>

    <!-- 数据列表 -->
    <div class="data-list-container">
      <div class="card-header-container">
        <h5 class="card-title">数据集列表</h5>
        <div class="header-actions">
          <el-button
            type="danger"
            :disabled="selectedIds.length === 0"
            @click="() => handleDeleteMultiple(selectedIds)"
          >
            删除
          </el-button>
          <el-button type="primary" @click="handleCreate">新建数据集</el-button>
        </div>
      </div>

      <!-- 批量操作栏 -->
      <div v-if="datasetList.length > 0" class="batch-actions">
        <el-checkbox
          v-model="isAllSelected"
          :indeterminate="isIndeterminate"
          @change="handleSelectAll"
        >
          全选
        </el-checkbox>
        <span class="selected-count">已选择 {{ selectedIds.length }} 个数据集</span>
      </div>

      <div class="grid-wrapper">
        <div class="card-grid" v-loading="loading">
          <Card
            v-for="item in datasetList"
            :key="item.id"
            :dataset="item"
            :selected="selectedIds.includes(item.id)"
            @select="(checked: boolean) => handleSelect(item.id, checked)"
            @update="handleUpdate(item)"
            @delete="handleDelete(item)"
          />
        </div>
      </div>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="PAGE_SIZES as number[]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>

    <!-- 创建数据集弹窗 -->
    <CreateDialog ref="createDialogRef" @success="handleCreateSuccess" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import Card from './components/Card.vue'
import Filter from './components/Filter.vue'
import CreateDialog from './components/CreateDialog.vue'
import { useList } from './useList'
import { PAGE_SIZES } from './constants'

defineOptions({ name: 'Dataset' })

const createDialogRef = ref()

const {
  loading,
  total,
  datasetList,
  queryParams,
  getList,
  handleQuery,
  handleUpdate,
  handleDelete,
  handleDeleteMultiple
} = useList()

const selectedIds = ref<number[]>([])

// 全选状态
const isAllSelected = computed({
  get: () =>
    datasetList.value.length > 0 && selectedIds.value.length === datasetList.value.length,
  set: (value: boolean) => {
    if (value) {
      selectedIds.value = datasetList.value.map((item) => item.id)
    } else {
      selectedIds.value = []
    }
  }
})

// 半选状态
const isIndeterminate = computed(() => {
  return selectedIds.value.length > 0 && selectedIds.value.length < datasetList.value.length
})

const handleSelectAll = (value: boolean) => {
  isAllSelected.value = value
}

const handleSelect = (id: number, checked: boolean) => {
  if (checked) {
    selectedIds.value.push(id)
  } else {
    selectedIds.value = selectedIds.value.filter((item) => item !== id)
  }
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.name = undefined
  queryParams.type = undefined
  handleQuery()
}

/** 新建按钮操作 */
const handleCreate = () => {
  createDialogRef.value?.open()
}

/** 创建成功回调 */
const handleCreateSuccess = () => {
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.header-actions {
  display: flex;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.selected-count {
  font-size: 14px;
  color: #8f959e;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 76px);
  box-sizing: border-box;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.data-list-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  padding: 14px 20px;
  flex: 1;
  box-sizing: border-box;
}
.grid-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-top: 14px;
}
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, 256px);
  gap: 20px;
  justify-content: flex-start;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
.filter-container {
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 0;
}
.card-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
