<template>
  <el-form
    :model="localParams"
    ref="queryFormRef"
    :inline="true"
  >
    <el-form-item label="名称" prop="name">
      <el-input
        v-model="localParams.name"
        placeholder="请输入搜索名称"
        clearable
        class="!w-240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="类型" prop="type">
      <el-select v-model="localParams.type" placeholder="请选择类型" clearable class="!w-240px">
        <el-option
          v-for="(label, value) in DATASET_TYPE_LABELS"
          :key="value"
          :label="label"
          :value="Number(value)"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { QueryParams } from '../types'
import { DATASET_TYPE_LABELS } from '../constants'

const props = defineProps<{
  queryParams: QueryParams
}>()

const emit = defineEmits<{
  query: []
  reset: []
}>()

const queryFormRef = ref()
const localParams = reactive({ ...props.queryParams })

watch(
  () => props.queryParams,
  (newVal) => {
    Object.assign(localParams, newVal)
  },
  { deep: true }
)

const handleQuery = () => {
  Object.assign(props.queryParams, localParams)
  emit('query')
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  emit('reset')
}
</script>

<style scoped>
</style>
