<template>
  <div class="file-list-card card">
    <div class="file-list-header">
      <h3 class="card-title">文件列表</h3>
      <div class="header-actions">
        <span class="upload-tips">{{ uploadTips }}</span>
        <div class="action-buttons">
          <el-button type="danger" :disabled="selectedFiles.length === 0" @click="handleDelete">
            删除
          </el-button>
          <el-button type="primary" @click="handleUpload">上传文件</el-button>
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="datasetFiles.length > 0" class="batch-actions">
      <el-checkbox
        v-model="isAllSelected"
        :indeterminate="isIndeterminate"
        @change="handleSelectAll"
      >
        全选
      </el-checkbox>
      <span class="selected-count">已选择 {{ selectedFiles.length }} 个文件</span>
    </div>

    <div class="file-grid-wrapper">
      <el-checkbox-group
        v-if="datasetFiles.length > 0"
        v-model="selectedFiles"
        class="file-grid"
        tag="div"
      >
        <div
          v-for="(file, index) in datasetFiles"
          :key="file.id"
          class="file-item-wrapper"
          :class="{ selected: selectedFiles.includes(index) }"
        >
          <el-checkbox :label="index" class="file-checkbox" />
          <div class="file-item">
            <!-- Video Icon -->
            <div
              v-if="file.type === DATASET_TYPES.VIDEO"
              class="file-placeholder video-placeholder"
            >
              <Icon icon="svg-icon:video" :size="48" />
              <div class="file-name" :title="file.fileName">{{ file.fileName }}</div>
            </div>
            <!-- Image Preview -->
            <img v-else-if="file.fileUrl" :src="file.fileUrl" :alt="file.fileName" />
            <!-- Default Placeholder -->
            <div v-else class="file-placeholder">
              <div class="file-icon">📄</div>
              <div class="file-name" :title="file.fileName">{{ file.fileName }}</div>
            </div>
          </div>
        </div>
      </el-checkbox-group>
      <div v-else class="empty-state">
        <div class="empty-icon">📁</div>
        <div class="empty-text">暂无文件</div>
        <div class="empty-desc">点击上传文件按钮添加文件</div>
      </div>
    </div>
    <div class="pagination-container">
      <el-pagination
        :current-page="queryParams.pageNo"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 隐藏的文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      multiple
      :accept="allowedFileTypes.map((type) => `.${type}`).join(',')"
      style="display: none"
      @change="handleFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Icon } from '@/components/Icon'
import { resumableUploadService } from '@/services/resumableUploadService'
import { isFileTypeAllowed } from '@/utils/uploadHelper'
import * as DatasetApi from '@/api/alg/dataset'
import type { Dataset, DatasetFile } from '../../list/types'
import { DATASET_TYPES } from '../../list/constants'
import { ALLOWED_EXTENSIONS, ALL_ALLOWED_EXTENSIONS } from './constants'

interface QueryParams {
  pageNo: number
  pageSize: number
}

interface Props {
  queryParams: QueryParams
  total: number
  datasetFiles: DatasetFile[]
  datasetId?: string
  dataset?: Dataset
}

interface Emits {
  (e: 'upload'): void
  (e: 'page-change', params: QueryParams): void
  (e: 'delete', fileIndexes: number[]): void
  (e: 'upload-success', fileUrl: string): void
  (e: 'refresh'): void
}

defineOptions({ name: 'DatasetFileManager' })

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 上传相关状态
const fileInputRef = ref<HTMLInputElement>()

// 根据数据集类型动态计算允许的文件类型
const allowedFileTypes = computed(() => {
  if (props.dataset?.type) {
    return ALLOWED_EXTENSIONS[props.dataset.type] || ALL_ALLOWED_EXTENSIONS
  }
  return ALL_ALLOWED_EXTENSIONS
})

// 上传提示
const uploadTips = computed(() => {
  const types = allowedFileTypes.value.join('/').toUpperCase()
  return `支持${types}格式`
})

// 选中的文件索引数组
const selectedFiles = ref<number[]>([])

// 全选状态
const isAllSelected = computed({
  get: () =>
    props.datasetFiles.length > 0 && selectedFiles.value.length === props.datasetFiles.length,
  set: (value: boolean) => {
    if (value) {
      selectedFiles.value = props.datasetFiles.map((_, index) => index)
    } else {
      selectedFiles.value = []
    }
  }
})

// 半选状态
const isIndeterminate = computed(() => {
  return selectedFiles.value.length > 0 && selectedFiles.value.length < props.datasetFiles.length
})

// 监听文件列表变化，清空选中状态
watch(
  () => props.datasetFiles,
  () => {
    selectedFiles.value = []
  },
  { deep: true }
)

const handleUpload = () => {
  // 触发文件选择
  fileInputRef.value?.click()
}

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files || files.length === 0) return

  // 处理多个文件
  Array.from(files).forEach((file) => {
    handleSingleFileUpload(file)
  })

  // 清空input值，允许重复选择同一文件
  target.value = ''
}

// 处理单个文件上传
const handleSingleFileUpload = async (file: File) => {
  // 文件类型检查
  if (!isFileTypeAllowed(file, allowedFileTypes.value)) {
    ElMessage.error(`不支持的文件类型，仅支持：${allowedFileTypes.value.join(', ')}`)
    return
  }


  // 开始断点续传上传
  try {
    await resumableUploadService.startUpload(
      file,
      props.datasetId ? Number(props.datasetId) : undefined,
      (progress) => {
        // 进度回调 - 可以在这里更新UI显示上传进度
        console.log(`${file.name} 上传进度: ${progress}%`)
      },
      async (fileUrl, fileInfo) => {
        // 成功回调 - 这里已经是complete-upload-part成功后了
        console.log(`${file.name} 上传成功:`, fileUrl, fileInfo)

        // 如果有数据集ID，则创建数据集文件记录
        if (props.datasetId && fileInfo) {
          try {
            await handleCreateDatasetFile(file, fileInfo)
            ElMessage.success(`${file.name} 上传并添加到数据集成功`)
          } catch (error) {
            console.error('创建数据集文件失败:', error)
            ElMessage.warning(`${file.name} 上传成功，但添加到数据集失败`)
          }
        } else {
          ElMessage.success(`${file.name} 上传成功`)
        }

        // 只触发一个刷新事件，避免重复刷新
        emit('refresh')
      },
      (error) => {
        // 错误回调
        console.error(`${file.name} 上传失败:`, error)
        ElMessage.error(`${file.name} 上传失败: ${error}`)
      }
    )
  } catch (error) {
    console.error('上传启动失败:', error)
    ElMessage.error('上传启动失败')
  }
}

const handleSelectAll = (value: boolean) => {
  isAllSelected.value = value
}

const handleDelete = async () => {
  if (selectedFiles.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 发送删除事件
    emit('delete', [...selectedFiles.value])

    // 清空选中状态
    selectedFiles.value = []

    // 显示成功提示
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleSizeChange = (size: number) => {
  emit('page-change', { pageNo: 1, pageSize: size })
}

const handleCurrentChange = (page: number) => {
  emit('page-change', { pageNo: page, pageSize: props.queryParams.pageSize })
}

// 创建数据集文件记录
const handleCreateDatasetFile = async (file: File, fileInfo: any) => {
  if (!props.datasetId) return

  try {
    // 直接使用 complete-upload-part 返回的 fileId
    const fileId = fileInfo.id

    // 创建数据集文件记录
    await DatasetApi.createDatasetFiles({
      datasetId: props.datasetId,
      fileVos: [
        {
          fileName: file.name,
          fileType: getFileTypeByExtension(file.name),
          fileId
        }
      ]
    })

    console.log('数据集文件创建成功')
  } catch (error) {
    console.error('创建数据集文件失败:', error)
    throw error
  }
}

// 根据文件扩展名获取文件类型
// 根据文件扩展名获取文件类型
const getFileTypeByExtension = (fileName: string): number => {
  const extension = fileName.split('.').pop()?.toLowerCase()
  if (!extension) return DATASET_TYPES.IMAGE // 默认为图片类型

  // 检查是否为视频
  const isVideo = ALLOWED_EXTENSIONS[DATASET_TYPES.VIDEO].includes(extension)
  if (isVideo) {
    return DATASET_TYPES.VIDEO
  }

  // 检查是否为图片
  const isImage = ALLOWED_EXTENSIONS[DATASET_TYPES.IMAGE].includes(extension)
  if (isImage) {
    return DATASET_TYPES.IMAGE
  }

  // 其他情况默认为图片
  return DATASET_TYPES.IMAGE
}
</script>

<style scoped>
.card {
  background-color: #fff;
  border-radius: 6px;
  padding: 20px;
}

.file-list-card {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 13px;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.upload-tips {
  font-size: 14px;
  color: #8f959e;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.selected-count {
  font-size: 14px;
  color: #8f959e;
}

.file-grid-wrapper {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, 256px);
  gap: 20px;
  justify-content: flex-start;
}

.file-item-wrapper {
  position: relative;
  width: 256px;
  height: 160px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.file-item-wrapper.selected {
  border: 2px solid #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.file-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 1;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-checkbox :deep(.el-checkbox__label) {
  display: none;
}

.file-item {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  background-color: #f0f2f5;
}

.file-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f0f2f5;
  border-radius: 4px;
}

.video-placeholder {
  background-color: #eef2f8;
}

.file-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.file-name {
  font-size: 12px;
  color: #606266;
  text-align: center;
  word-break: break-all;
  padding: 20px 8px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #8f959e;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #1f2329;
}

.empty-desc {
  font-size: 14px;
}
</style>
