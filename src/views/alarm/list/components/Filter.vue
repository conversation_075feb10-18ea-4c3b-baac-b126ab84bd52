<template>
  <el-form :model="localParams" ref="queryFormRef" :inline="true">
    <el-form-item label="名称" prop="name">
      <el-input
        v-model="localParams.name"
        placeholder="请输入任务名称"
        clearable
        class="!w-240px"
        @keyup.enter="handleQuery"
      />
    </el-form-item>
    <el-form-item label="告警类型" prop="alarmTypeName">
      <el-select
        v-model="localParams.alarmTypeName"
        placeholder="全部"
        clearable
        class="!w-160px"
      >
        <el-option label="安全帽识别" value="安全帽识别" />
        <el-option label="反光衣识别" value="反光衣识别" />
        <el-option label="烟火识别" value="烟火识别" />
        <el-option label="人员入侵" value="人员入侵" />
        <el-option label="车辆识别" value="车辆识别" />
      </el-select>
    </el-form-item>
    <el-form-item label="状态" prop="status">
      <el-select
        v-model="localParams.status"
        placeholder="全部"
        clearable
        class="!w-140px"
      >
        <el-option label="待处理" :value="0" />
        <el-option label="处理中" :value="1" />
        <el-option label="已处理" :value="2" />
        <el-option label="已忽略" :value="3" />
      </el-select>
    </el-form-item>
    <el-form-item label="创建时间" prop="createTime">
      <el-date-picker
        v-model="localParams.createTime"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        class="!w-240px"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleQuery">搜索</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { QueryParams } from '../types'

const props = defineProps<{
  queryParams: QueryParams
}>()

const emit = defineEmits<{
  query: []
  reset: []
}>()

const queryFormRef = ref()
const localParams = reactive({ ...props.queryParams })

watch(
  () => props.queryParams,
  (newVal) => {
    Object.assign(localParams, newVal)
  },
  { deep: true }
)

const handleQuery = () => {
  Object.assign(props.queryParams, localParams)
  emit('query')
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  emit('reset')
}
</script>
