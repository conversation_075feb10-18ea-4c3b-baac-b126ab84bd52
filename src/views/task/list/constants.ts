// 任务相关常量
export const TASK_TYPES = {
  DATASET: 0,
  VIDEO_STREAM: 1
} as const

export const TASK_TYPE_LABELS = {
  [TASK_TYPES.DATASET]: '数据集',
  [TASK_TYPES.VIDEO_STREAM]: '视频流'
} as const

// 任务状态枚举（根据实际业务调整）
export const TASK_STATUS = {
  RUNNING: 0,
  SUCCESS: 10,
  FAILED: 20,
  PENDING: 30
} as const

export const TASK_STATUS_LABELS = {
  [TASK_STATUS.RUNNING]: '正在执行',
  [TASK_STATUS.SUCCESS]: '执行完成',
  [TASK_STATUS.FAILED]: '执行失败',
  [TASK_STATUS.PENDING]: '待执行'
} as const

export const TASK_STATUS_COLORS = {
  [TASK_STATUS.RUNNING]: 'warning',
  [TASK_STATUS.SUCCESS]: 'success',
  [TASK_STATUS.FAILED]: 'danger',
  [TASK_STATUS.PENDING]: 'info'
} as const

export const PAGE_SIZES = [10, 20, 50, 100]
