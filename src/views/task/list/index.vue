<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container mb-3">
      <Filter :query-params="queryParams" @query="handleQuery" @reset="resetQuery" />
    </div>

    <!-- 数据列表 -->
    <div class="data-list-container">
      <div class="card-header-container">
        <h5 class="card-title">任务列表</h5>
        <div class="header-actions">
          <el-button
            type="danger"
            :disabled="selectedIds.length === 0"
            @click="() => handleDeleteMultiple(selectedIds)"
          >
            删除
          </el-button>
          <el-button type="primary" @click="handleCreate">创建任务</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <div class="table-wrapper">
        <el-table
          :data="taskList"
          v-loading="loading"
          style="width: 100%"
          :header-cell-style="{ backgroundColor: '#f2f3f5' }"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="任务名称" min-width="150" />
          <el-table-column prop="taskType" label="数据类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTaskTypeTagType(row.taskType)">
                {{ getTaskTypeLabel(row.taskType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="taskDataName" label="数据名称" min-width="120" />
          <el-table-column prop="aiModelName" label="算法名称" min-width="120" />
          <el-table-column prop="alarmNoticeUserName" label="预警通知" min-width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <!-- 执行/终止按钮 -->
              <el-button
                v-if="row.status === TASK_STATUS.RUNNING"
                link
                type="warning"
                @click="handleStop(row)"
              >
                终止
              </el-button>
              <el-button
                v-else-if="row.status === TASK_STATUS.PENDING"
                link
                type="primary"
                @click="handleStart(row)"
              >
                执行
              </el-button>

              <!-- 编辑按钮：仅待执行状态可编辑 -->
              <el-button
                v-if="row.status === TASK_STATUS.PENDING"
                link
                type="primary"
                @click="handleUpdate(row)"
              >
                编辑
              </el-button>

              <!-- 删除按钮：仅待执行状态可删除 -->
              <el-button
                v-if="row.status === TASK_STATUS.PENDING"
                link
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="PAGE_SIZES as number[]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getList"
          @current-change="getList"
        />
      </div>
    </div>

    <!-- 创建/编辑任务弹窗 -->
    <TaskFormDialog ref="formDialogRef" @success="getList" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue'
import Filter from './components/Filter.vue'
import TaskFormDialog from './components/CreateDialog.vue'
import { useList } from './useList'
import {
  PAGE_SIZES,
  TASK_STATUS,
  TASK_STATUS_LABELS,
  TASK_STATUS_COLORS,
  TASK_TYPE_LABELS,
  TASK_TYPES
} from './constants'
import { formatDate } from '@/utils/formatTime'
import type { Task } from './types'

defineOptions({ name: 'Task' })

const formDialogRef = ref()

const {
  loading,
  total,
  taskList,
  queryParams,
  getList,
  handleQuery,
  handleDelete,
  handleDeleteMultiple,
  handleStart,
  handleStop
} = useList()

const selectedIds = ref<number[]>([])

const handleSelectionChange = (selection: Task[]) => {
  selectedIds.value = selection.map((item) => item.id)
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.keyword = undefined
  queryParams.taskType = undefined
  queryParams.status = undefined
  queryParams.createTime = undefined
  handleQuery()
}

/** 新建按钮操作 */
const handleCreate = () => {
  formDialogRef.value?.open()
}

/** 编辑按钮操作 */
const handleUpdate = (row: Task) => {
  formDialogRef.value?.open(row)
}

/** 获取状态标签 */
const getStatusLabel = (status: number) => {
  return TASK_STATUS_LABELS[status] || '未知'
}

/** 获取状态颜色 */
const getStatusColor = (status: number) => {
  return TASK_STATUS_COLORS[status] || 'info'
}

/** 获取任务类型标签 */
const getTaskTypeLabel = (type: number) => {
  return TASK_TYPE_LABELS[type] || '未知'
}

/** 获取任务类型主题 */
const getTaskTypeTagType = (type: number) => {
  if (type === TASK_TYPES.DATASET) {
    return 'primary'
  }
  if (type === TASK_TYPES.VIDEO_STREAM) {
    return 'success'
  }
  return 'info'
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.header-actions {
  display: flex;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 76px);
  box-sizing: border-box;
}
.mb-4 {
  margin-bottom: 16px;
}
.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
.data-list-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  padding: 14px 20px;
  flex: 1;
  box-sizing: border-box;
}
.table-wrapper {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-top: 14px;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}
.filter-container {
  background-color: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 20px;
  padding-bottom: 0;
}
.card-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
