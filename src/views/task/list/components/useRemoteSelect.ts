import { ref } from 'vue'
import { ElMessage } from 'element-plus'

type ApiFunc<T> = (params: any) => Promise<{ list: T[] }>

export function useRemoteSelect<T extends { id: number }>(
  apiFunc: ApiFunc<T>,
  listKey: string,
  initialParams: Record<string, any> = {},
  errorMsg: string = '获取列表失败'
) {
  const list = ref<T[]>([])
  const loading = ref(false)

  const search = async (query: string) => {
    loading.value = true
    try {
      const params: Record<string, any> = { ...initialParams, pageSize: 20, pageNo: 1 }
      params[listKey] = query
      const res = await apiFunc(params)
      list.value = res.list
    } catch (error) {
      ElMessage.error(errorMsg)
      console.error(errorMsg, error)
    } finally {
      loading.value = false
    }
  }

  return {
    list,
    loading,
    search
  }
}
