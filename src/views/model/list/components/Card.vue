<template>
  <div class="model-card" @click="handleDetail">
    <el-checkbox
      :model-value="selected"
      class="card-checkbox"
      @click.stop
      @change="(checked: boolean) => emit('select', checked)"
    />
    <img class="card-image" :src="model.coverImageFileUrl || DEFAULT_COVER_URL" />
    <span class="card-title">{{ model.modelName }}</span>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import type { Model } from '../types'
import { DEFAULT_COVER_URL } from '../constants'

const props = defineProps<{
  model: Model
  selected: boolean
}>()

const emit = defineEmits<{
  (e: 'select', checked: boolean): void
}>()

const router = useRouter()
const handleDetail = () => {
  // TODO: 跳转到模型详情页
  // router.push({ name: 'ModelDetail', query: { id: props.model.id } })
}
</script>

<style scoped>
.model-card {
  position: relative;
  width: 256px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 1;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-checkbox :deep(.el-checkbox__label) {
  display: none;
}

.card-image {
  width: 100%;
  height: 160px;
  border-radius: 4px;
  object-fit: cover;
  background-color: #f0f2f5;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  color: #1f2329;
  white-space: pre;
  margin: 16px 0 12px;
}

</style>
