<template>
  <el-dialog
    v-model="dialogVisible"
    title="视频流详情"
    width="700px"
    :before-close="handleClose"
    append-to-body
    class="video-stream-dialog"
  >
    <div class="dialog-content">
      <!-- 视频预览区域 -->
      <div class="video-frame">
        <img
          src="https://seal-img.nos-jd.163yun.com/obj/w5rCgMKVw6DCmGzCmsK-/61748886074/2c9a/90d5/5561/36cf2bc4247066f0d543c4e11a57b27b.png"
          alt="视频预览"
          class="video-image"
        />
        <!-- 设备信息叠加层 -->
        <div class="video-overlay">
          <div class="device-info-overlay">
            <div class="info-group">
              <span class="info-label-overlay">视频名称：</span>
              <span class="info-value-overlay">{{ deviceData.deviceName || '1号摄像头' }}</span>
            </div>
            <div class="info-group">
              <span class="info-label-overlay">视频URL：</span>
              <span class="info-value-overlay">{{
                deviceData.liveUrl || 'http://www.baidu.com'
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单区域 -->
      <el-form :model="formData" label-width="80px" class="device-form" label-position="left">
        <el-form-item label="用户名" class="form-item">
          <el-input v-model="formData.username" placeholder="请输入用户名" class="form-input" />
        </el-form-item>
        <el-form-item label="密码" class="form-item">
          <div class="password-group">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              class="form-input"
              show-password
            />
            <el-button type="primary" size="small" class="test-button" @click="handleTest">
              测试
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'VideoStreamDialog' })

interface DeviceData {
  deviceName?: string
  liveUrl?: string
  username?: string
  password?: string
}

const emit = defineEmits<{
  success: []
}>()

const dialogVisible = ref(false)
const loading = ref(false)

const deviceData = ref<Partial<DeviceData>>({})

const formData = reactive({
  username: '',
  password: ''
})

/** 打开弹窗 */
const open = (data?: DeviceData) => {
  deviceData.value = data || {}
  dialogVisible.value = true
  resetForm()
}

/** 关闭弹窗 */
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  formData.username = deviceData.value.username || ''
  formData.password = deviceData.value.password || ''
}

/** 测试连接 */
const handleTest = async () => {
  if (!formData.username || !formData.password) {
    ElMessage.warning('请输入用户名和密码')
    return
  }

  try {
    // TODO: 调用测试连接的API
    // await testConnection({
    //   url: deviceData.value.liveUrl,
    //   username: formData.username,
    //   password: formData.password
    // })

    ElMessage.success('连接测试成功')
  } catch (error) {
    ElMessage.error('连接测试失败')
    console.error('连接测试失败:', error)
  }
}

/** 确认保存 */
const handleConfirm = async () => {
  loading.value = true
  try {
    // TODO: 调用保存设备信息的API
    // await saveDeviceInfo({
    //   ...deviceData.value,
    //   username: formData.username,
    //   password: formData.password
    // })

    ElMessage.success('保存成功')
    handleClose()
    emit('success')
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('保存设备信息失败:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
/* 视频容器 */
.video-frame {
  position: relative;
  width: 100%;
  height: 360px; /* 调整视频高度 */
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.video-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* 视频叠加信息层 */
.video-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  padding: 12px 18px;
}

.device-info-overlay {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label-overlay {
  font-size: 14px;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  line-height: 16px;
  color: #ffffff;
  white-space: nowrap;
}

.info-value-overlay {
  font-size: 14px;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  line-height: 16px;
  color: #ffffff;
  white-space: nowrap;
}

/* 表单容器 */
.device-form {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.form-item {
  margin-bottom: 18px; /* 调整表单项间距 */
}

:deep(.form-item .el-form-item__label) {
  font-size: 14px;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  line-height: 32px;
  color: #646a73;
  padding: 0;
  width: 80px !important;
}

.form-input {
  width: 100%;
}

.password-group .form-input {
  flex: 1;
}

:deep(.form-input .el-input__wrapper) {
  height: 32px;
  padding: 7px 11px;
  border-radius: 4px;
  border: 1px solid #dddee1;
  background-color: #ffffff;
}

:deep(.form-input .el-input__inner) {
  font-size: 14px;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  line-height: 16px;
  color: #1f2329;
}

:deep(.form-input .el-input__inner::placeholder) {
  color: #8f959e;
}

.password-group {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.test-button {
  height: 32px;
  padding: 8px 16px;
  border-radius: 4px;
  background-color: #0057d9;
  border: none;
  font-size: 14px;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  font-weight: 400;
  line-height: 16px;
  color: #ffffff;
  flex-shrink: 0;
}

.test-button:hover {
  background-color: #0046b3;
}

/* 底部按钮 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 20px 0;
}

:deep(.dialog-footer .el-button) {
  width: 88px;
  height: 34px;
  padding: 8px 15px;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  font-weight: 400;
  line-height: 16px;
}

:deep(.dialog-footer .el-button--default) {
  border: 1px solid #d8d8d8;
  background-color: #ffffff;
  color: #222529;
}

:deep(.dialog-footer .el-button--primary) {
  background-color: #0057d9;
  border: none;
  color: #ffffff;
}
</style>
