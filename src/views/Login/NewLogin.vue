<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElLoading, FormInstance, FormRules } from 'element-plus'
import * as authUtil from '@/utils/auth'
import { usePermissionStore } from '@/store/modules/permission'
import * as LoginApi from '@/api/login'
import { useMessage } from '@/hooks/web/useMessage'

const { push } = useRouter()
const message = useMessage()
const permissionStore = usePermissionStore()
const formLogin = ref<FormInstance>()
const loginLoading = ref(false)

const loginData = reactive({
  loginForm: {
    username: import.meta.env.VITE_APP_DEFAULT_LOGIN_USERNAME || 'admin',
    password: import.meta.env.VITE_APP_DEFAULT_LOGIN_PASSWORD || 'admin123',
    captchaVerification: '' // API 需要，保持为空字符串
  }
})

const loginRules = reactive<FormRules>({
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
})

const handleLogin = async () => {
  const valid = await formLogin.value?.validate()
  if (!valid) return

  loginLoading.value = true
  const loading = ElLoading.service({
    lock: true,
    text: '正在登录中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    // 租户相关的逻辑已被移除
    const res = await LoginApi.login(loginData.loginForm)
    authUtil.setToken(res)

    await push({ path: '/' })
  } finally {
    loginLoading.value = false
    loading.close()
  }
}

onMounted(() => {
  const cachedForm = authUtil.getLoginForm()
  if (cachedForm) {
    loginData.loginForm.username = cachedForm.username || loginData.loginForm.username
  }
})
</script>

<template>
  <div class="login-container">
    <div class="login-bg">
      <div class="login-panel">
        <div class="login-header">
          <div class="logo">
            <img
              class="logo__image"
              src="https://seal-img.nos-jd.163yun.com/obj/w5rCgMKVw6DCmGzCmsK-/61694808628/256c/743c/340c/751ca45df83ae23fe694a0ae42c3f750.png"
            />
            <div class="logo__bg"></div>
          </div>
          <span class="header-title">AI算法联盟</span>
        </div>

        <div class="login-title">
          <span class="login-title__text">密码登录</span>
          <div class="login-title__underline"></div>
        </div>

        <el-form
          ref="formLogin"
          :model="loginData.loginForm"
          :rules="loginRules"
          class="login-form"
          label-position="top"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username" label="用户名">
            <el-input v-model="loginData.loginForm.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item prop="password" label="密码">
            <el-input
              v-model="loginData.loginForm.password"
              type="password"
              show-password
              placeholder="请输入密码"
            />
          </el-form-item>
          <el-button
            class="login-button"
            type="primary"
            :loading="loginLoading"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form>

        <div class="agreement">
          <img
            class="agreement__icon"
            src="https://seal-img.nos-jd.163yun.com/obj/w5rCgMKVw6DCmGzCmsK-/61790019812/0028/3e59/7c71/47f711d63a61e41d727b275e07735214.png"
          />
          <div class="agreement__text">
            <span>我已阅读并同意</span>
            <span class="agreement__link">《用户服务协议》《隐私政策》</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  width: 100%;
  height: 100vh;
  background-color: #f3f5f6;
}

.login-bg {
  width: 100%;
  height: 100%;
  background: url(https://seal-img.nos-jd.163yun.com/obj/w5rCgMKVw6DCmGzCmsK-/61790022063/f651/4881/91d0/21d3bed6930405d3e08da7328672177e.png)
    center / cover no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-panel {
  width: 400px;
  height: 500px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.94);
  box-shadow: 0 4px 10px rgba(0, 26, 64, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 36px 40px;
  box-sizing: border-box;
}

.login-header {
  display: flex;
  align-items: center;
  margin-bottom: 36px;
}

.logo {
  position: relative;
  width: 48px;
  height: 48px;
  margin-right: 12px;
}

.logo__image {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.logo__bg {
  position: absolute;
  top: 4px;
  left: 4px;
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 6px;
}

.header-title {
  font-size: 20px;
  font-weight: 700;
  color: #222529;
  font-family: 'HarmonyOS Sans', sans-serif;
}

.login-title {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 22px;
}

.login-title__text {
  font-size: 16px;
  font-weight: 500;
  color: #0057d9;
  font-family: 'HarmonyOS Sans', sans-serif;
}

.login-title__underline {
  width: 64px;
  height: 2px;
  background-color: #0057d9;
  margin-top: 14px;
}

.login-form {
  width: 100%;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.login-form :deep(.el-form-item__label) {
  padding: 0;
  margin-bottom: 15px;
  font-size: 14px;
  color: #595e66;
  line-height: 1;
  font-family: 'HarmonyOS Sans', sans-serif;
}

.login-form :deep(.el-form-item.is-required .el-form-item__label::before) {
  display: none;
}

.login-form :deep(.el-input__wrapper) {
  height: 54px;
  padding: 0 15px;
  border: 1px solid #e1e8f0;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: none !important;
}

.login-form :deep(.el-input__inner) {
  font-size: 16px;
  color: #222529;
  font-family: 'HarmonyOS Sans', sans-serif;
}

.login-button {
  width: 100%;
  height: 40px;
  margin-top: 16px;
  font-size: 16px;
  font-family: 'HarmonyOS Sans', sans-serif;
}

.agreement {
  display: flex;
  align-items: center;
  margin-top: 32px;
  font-size: 14px;
  font-family: 'HarmonyOS Sans', sans-serif;
  color: #727e91;
}

.agreement__icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.agreement__link {
  color: #0057d9;
  cursor: pointer;
}
</style>
